{"expo": {"name": "VitalCare", "slug": "VITALCARE", "version": "1.0.15", "orientation": "portrait", "icon": "./src/assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"googleServicesFile": "./GoogleService-Info.plist", "entitlements": {"aps-environment": "production", "com.apple.developer.pushkit.unrestricted-voip": true, "com.apple.developer.usernotifications.critical-alerts": true}, "supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "VitalCare needs camera access so you can take photos of wounds, injuries, or lab results to share securely with your care team.", "NSMicrophoneUsageDescription": "VitalCare needs microphone access so you can speak with your clinician during telehealth calls.", "NSCriticalAlertsUsageDescription": "VitalCare needs to play emergency sounds even when the device is muted.", "UIBackgroundModes": ["audio", "voip", "remote-notification"], "CFBundleDisplayName": "VitalCare", "LSApplicationQueriesSchemes": ["itms-apps"], "NSPhotoLibraryUsageDescription": "VitalCare needs access to your photo library so you can select a profile picture and upload images for visits.", "NSPhotoLibraryAddUsageDescription": "We need permission to save photos you take in VitalCare."}, "bundleIdentifier": "com.austinr47.VITALCARE"}, "android": {"googleServicesFile": "./google-services.json", "adaptiveIcon": {"foregroundImage": "./src/assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["CAMERA", "RECORD_AUDIO", "CAMERA", "RECORD_AUDIO", "POST_NOTIFICATIONS"], "package": "com.austinr47.VITALCARE"}, "web": {"bundler": "metro", "output": "static", "favicon": "./src/assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./src/assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#0279EB"}], ["expo-build-properties", {"ios": {"newArchEnabled": false, "flipper": false, "useFrameworks": "static", "entitlements": {"aps-environment": "production", "com.apple.developer.pushkit.unrestricted-voip": true, "com.apple.developer.usernotifications.critical-alerts": true}}, "android": {"newArchEnabled": false, "compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "34.0.0"}}], "@react-native-firebase/app", "@react-native-firebase/messaging"], "experiments": {"typedRoutes": true}, "owner": "vitalcare", "runtimeVersion": "1.0.15", "updates": {"url": "https://u.expo.dev/bb33e355-99c6-43e8-9672-4d943d4258cd", "enabled": true, "checkAutomatically": "ON_LOAD", "fallbackToCacheTimeout": 0}}}