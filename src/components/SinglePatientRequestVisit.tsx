import { Facility } from "src/hooks/useFacilities";
import { Text, TextArea, View, YStack } from "tamagui";
import FacilityDrawer from "./FacilityDrawer";
import PatientSearchDrawer from "./PatientSearchDrawer";
import { useSinglePatientRequestVisitStyles } from "./componentstyles/SinglePatientRequestVisitStyles";
import { useState, useRef } from "react";
import { PatientInfo, SelectedImage } from "@/nurse/requestVisit";
import { usePatients } from "src/hooks/usePatientsByFacilities";
import { Platform, View as RNView } from "react-native";
import { formatDOB } from "./AddPatientDialog";
import AddDocuments from "./AddDocuments";

interface SinglePatientRequestVisitProps {
  selectedPatients: PatientInfo[];
  setselectedPatients: (patients: PatientInfo[]) => void;
  facilities: Facility[];
  facilitiesLoading: boolean;
  facilitiesError: any;
  ehrType: string;
  chiefComplaint: string;
  setChiefComplaint: (text: string) => void;
  scannedImages: SelectedImage[];
  handleAddDocument: () => void;
  handleRemoveImage: (url: string) => void;
  onScrollToPosition?: (yPosition: number) => void;
}

const SinglePatientRequestVisit = ({
  selectedPatients,
  setselectedPatients,
  facilities,
  facilitiesLoading,
  facilitiesError,
  ehrType,
  chiefComplaint,
  setChiefComplaint,
  scannedImages,
  handleAddDocument,
  handleRemoveImage,
  onScrollToPosition,
}: SinglePatientRequestVisitProps) => {
  const styles = useSinglePatientRequestVisitStyles();
  const chiefComplaintRef = useRef<RNView>(null);

  const handleChiefComplaintFocus = () => {
    if (chiefComplaintRef.current && onScrollToPosition) {
      chiefComplaintRef.current.measureInWindow((_x, y, _width, _height) => {
        onScrollToPosition(y - 100); // Scroll to position with some offset from top
      });
    }
  };

  const [patientNameSearch, setPatientNameSearch] = useState<string>("Al");
  const [selectedFacilityId, setSelectedFacilityId] = useState<string>("");
  const handleFacilitySelect = (facilityId: string) => {
    setSelectedFacilityId(facilityId);
  };

  const {
    data: patients,
    isLoading: patientsLoading,
    error: patientsError,
  } = usePatients(selectedFacilityId || "", patientNameSearch);

  const handlePatientSelect = (
    patientId: string,
    patientFirstName: string,
    dob: string
  ) => {
    setselectedPatients([
      {
        id: patientId,
        name: patientFirstName,
        dob,
        facilityId: selectedFacilityId,
      },
    ]);
  };

  return (
    <View>
      <YStack>
        <Text {...styles.facilityTitle}>Facility</Text>
        <YStack>
          {facilitiesLoading ? (
            <Text>Loading facilities...</Text>
          ) : facilitiesError ? (
            <Text>Error loading facilities</Text>
          ) : (
            <FacilityDrawer
              data={facilities || []}
              placeholder="Select a Facility"
              onSelect={(id: string) => handleFacilitySelect(id)}
            />
          )}
        </YStack>
      </YStack>

      <YStack {...styles.patientContainer}>
        {ehrType !== "manual" && <Text {...styles.facilityTitle}>Patient</Text>}
        {ehrType === "manual" ? (
          <YStack width="100%">
            <Text {...styles.facilityTitle}>Patient</Text>
            <TextArea
              {...styles.patientNameTextArea}
              placeholder="Type Patient Name"
              placeholderTextColor="$textcolor"
              overflow="hidden"
              value={selectedPatients[0]?.name}
              onChangeText={(text) => {
                setselectedPatients([
                  {
                    id: text,
                    name: text,
                    dob: selectedPatients[0]?.dob || "",
                    facilityId:
                      selectedPatients[0]?.facilityId || selectedFacilityId,
                  },
                ]);
              }}
            />
            <Text {...styles.facilityTitle} mt={"$4"}>
              Date of Birth (MM/DD/YYYY)
            </Text>
            <TextArea
              {...styles.patientNameTextArea}
              placeholder="MM-DD-YYYY"
              placeholderTextColor="$textcolor"
              keyboardType={Platform.OS === "ios" ? "number-pad" : "numeric"}
              maxLength={10}
              value={selectedPatients[0]?.dob || ""}
              onChangeText={(text) => {
                const dob = formatDOB(text);
                setselectedPatients([{ ...selectedPatients[0], dob }]);
              }}
            />
          </YStack>
        ) : (
          <YStack marginBlockEnd={50}>
            <PatientSearchDrawer
              data={patients || []}
              placeholder="Select a Patient"
              onSelect={(id: string, firstName: string, dob: string) =>
                handlePatientSelect(id, firstName, dob)
              }
              onSearch={(query: string) => {
                setPatientNameSearch(query);
                return Promise.resolve();
              }}
              disabled={!selectedFacilityId}
              loading={patientsLoading}
              error={patientsError?.message || ""}
            />
          </YStack>
        )}
      </YStack>
      <View ref={chiefComplaintRef}>
        <Text {...styles.complaintText}>Chief complaint</Text>
        <TextArea
          {...styles.complaintTextArea}
          placeholder="Please enter the details."
          placeholderTextColor={"$textcolor"}
          overflow="hidden"
          value={chiefComplaint}
          onChangeText={setChiefComplaint}
          onFocus={handleChiefComplaintFocus}
        />
      </View>
      <YStack>
        <Text {...styles.attachmentsText}>Attachments</Text>
        <AddDocuments
          onAddDocument={handleAddDocument}
          scannedImages={scannedImages}
          onRemoveImage={handleRemoveImage}
          patientName={selectedPatients[0]?.name}
        />
      </YStack>
    </View>
  );
};

export default SinglePatientRequestVisit;
