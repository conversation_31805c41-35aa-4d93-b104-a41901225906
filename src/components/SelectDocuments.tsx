import { <PERSON><PERSON>, <PERSON>ton, YStack, Text, XStack } from "tamagui";
import { <PERSON>ert, Keyboard, Pressable } from "react-native";
import { Camera, Image as Gallery } from "@tamagui/lucide-icons";
import * as ImagePicker from "expo-image-picker";
import { SelectedImage } from "@/nurse/requestVisit";

interface SelectDocumentsProps {
  open: boolean;
  onClose: (open: boolean) => void;
  onOpenScanDocument?: () => void;
  onAddImage?: (uri: string) => void;
  scannedImages: SelectedImage[];
}

export function SelectDocument({
  open,
  onClose,
  onOpenScanDocument,
  onAddImage,
  scannedImages,
}: SelectDocumentsProps): JSX.Element {
  const renderOptionCard = (
    IconComponent: React.ElementType,
    label: string,
    onPress: () => void
  ) => (
    <Pressable onPress={onPress} {...styles.card}>
      <YStack {...styles.cardBody}>
        <IconComponent {...styles.icon} />
        <Text {...styles.labeltext}>{label}</Text>
      </YStack>
    </Pressable>
  );

  const handleCamera = async () => {
    if (!(await ensurePermissions())) return;
    if (onOpenScanDocument) onOpenScanDocument();
  };

  async function ensurePermissions() {
    const { status: camStatus } =
      await ImagePicker.requestCameraPermissionsAsync();
    const { status: libStatus } =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (camStatus !== "granted" || libStatus !== "granted") {
      Alert.alert(
        "Permission needed",
        "Camera + Photo Library permissions are required to change your profile picture."
      );
      return false;
    }
    return true;
  }

  const pickImage = async () => {
    if (!(await ensurePermissions())) return;

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      if (onAddImage) {
        onAddImage(result.assets[0].uri);
      }
      onClose(false);
    }
  };

  const styles = useSelectDocumentStyle();

  return (
    <Dialog modal open={open} onOpenChange={onClose}>
      <Dialog.Overlay {...styles.overlay} />
      <Dialog.Content {...styles.dialogContent}>
        <YStack {...styles.container}>
          <XStack {...styles.headerContainer}>
            <Text {...styles.headerText}>Attach a file</Text>
          </XStack>
        </YStack>
        <YStack {...styles.body}>
          <XStack {...styles.options}>
            {renderOptionCard(Camera, "Camera", handleCamera)}
            {renderOptionCard(Gallery, "Gallery", pickImage)}
          </XStack>
          <Button {...styles.cancelBtn} onPress={() => onClose(false)}>
            Cancel
          </Button>
        </YStack>
      </Dialog.Content>
    </Dialog>
  );
}

export const useSelectDocumentStyle = () => {
  return {
    dialogContent: {
      bordered: true,
      bg: "$screenBackgroundcolor" as any,
      br: "$4" as any,
      shadowColor: "transparent" as any,
      shadowOpacity: 0 as any,
      shadowRadius: 0 as any,
      width: "90%" as any,
      alignSelf: "center" as any,
      justifyContent: "center" as any,
      borderRadius: "$7" as any,
      position: "absolute" as any,
      top: "50%" as any,
      left: "50%" as any,
      transform: [{ translateX: "-50%" }, { translateY: "-50%" }] as any,
      borderColor: "$primaryBorderColor" as any,
    },
    container: {
      gap: "$2" as any,
    },
    headerContainer: {
      justifyContent: "space-between",
    },
    headerText: {
      fontSize: 24,
      fontWeight: "600" as any,
    },
    overlay: {
      animation: "quick" as any,
      enterStyle: { opacity: 0 },
      exitStyle: { opacity: 0 },
      backgroundColor: "black" as any,
      opacity: 0.6,
    },
    card: {
      width: "48%",
      height: "200%",
    },
    cardBody: {
      backgroundColor: "#F7F8FB",
      borderRadius: 12,
      alignItems: "center",
      justifyContent: "center",
      flex: 1,
      borderWidth: 1,
      borderColor: "#D4D8DA" as any,
    },
    labeltext: {
      color: "Black" as any,
      fontSize: 14,
      fontWeight: 500 as any,
      marginBlockStart: 10,
    },
    options: {
      marginBlockStart: 50,
      justifyContent: "space-between",
      alignItems: "center",
      gap: "$2" as any,
    },
    body: {
      marginBlockStart: 0,
    },
    cancelBtn: {
      backgroundColor: "transparent",
      borderRadius: 7,
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      marginBlockStart: 50,
      color: "$textcolor" as any,
    },
    icon: {
      size: "$2",
      color: "black",
    },
  };
};
