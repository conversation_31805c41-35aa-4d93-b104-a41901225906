export const useSinglePatientRequestVisitStyles = () => {
  return {
    patientContainer: {
      marginBlock: 20,
    },
    facilityTitle: {
      fontWeight: 500 as any,
      fontSize: 14,
      marginBlockEnd: 10,
    },
    patientNameTextArea: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
      width: "100%" as "100%",
      borderWidth: 1,
      borderRadius: 7,
      color: "$textcolor" as any,
      fontWeight: 500 as any,
      padding: 10,
      fontSize: 16,
      numberOfLines: 6,
      textAlignVertical: "top" as any,
    },
    complaintText: {
      fontSize: 14,
      fontWeight: 500 as any,
      color: "$textcolor" as any,
      marginBlockStart: 10,
      marginBlockEnd: 10,
    },
    complaintTextArea: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
      size: "$1" as "$1",
      borderWidth: 1,
      borderRadius: 7,
      color: "$textcolor" as any,
      fontWeight: 500 as any,
      padding: 10,
      fontSize: 16,
      numberOfLines: 8,
      textAlignVertical: "top" as any,
    },
    attachmentsText: {
      fontSize: 14,
      fontWeight: 500 as any,
      color: "$textcolor" as any,
      marginBlockStart: 20,
      marginBlockEnd: 5,
    },
  };
};
