import UserAlerts from "@/common/userAlerts";
import { ArrowLeft, X } from "@tamagui/lucide-icons";
import { useEffect, useState } from "react";
import { Pressable } from "react-native";
import { Avatar, Button, Text, XStack, YStack } from "tamagui";
import { useAuth } from "~/context/AuthContext";

interface HeaderProps {
  onAvatarPress?: () => void;
  onBackPress?: () => void;
  screenName: string;
  subText?: string;
  shouldNavibgateToChat?: boolean;
  consultationId?: string;
  shouldSHowChatIcon?: boolean;
  shouldShowBackButton?: boolean;
  shouldShowAvatar?: boolean;
}

export default function ScreenHeader({
  onAvatarPress,
  screenName,
  onBackPress,
  subText,
  shouldNavibgateToChat = false,
  consultationId,
  shouldSHowChatIcon = true,
  shouldShowBackButton = true,
  shouldShowAvatar = true,
}: HeaderProps) {
  const { user, localUser } = useAuth();
  const initials = user
    ? user?.firstName?.charAt(0) + user?.lastName?.charAt(0)
    : "User";
  const [profileImage, setProfileImage] = useState<string>("");
  useEffect(() => {
    if (
      localUser?.profilePicUrl !== null &&
      localUser?.profilePicUrl !== undefined &&
      localUser?.profilePicUrl !== ""
    ) {
      setProfileImage(localUser?.profilePicUrl);
    }
  }, [localUser?.profilePicUrl]);

  return (
    <XStack {...stackStyles.container}>
      <XStack {...stackStyles.leftSection}>
        {shouldShowBackButton && (
          <Button
            {...buttonStyles.backButton}
            icon={ArrowLeft}
            onPress={onBackPress}
          />
        )}
        {subText ? (
          <YStack>
            <Text {...textStyles.screenName}>{screenName} </Text>
            <Text {...textStyles.subText}>{subText}</Text>
          </YStack>
        ) : (
          <Text {...textStyles.screenName}>{screenName}</Text>
        )}
      </XStack>

      <XStack {...avatarStyles.messageContainer}>
        {shouldSHowChatIcon && (
          <UserAlerts
            shouldNavibgateToChat={shouldNavibgateToChat}
            consultationId={consultationId}
          />
        )}
        {shouldShowAvatar && (
          <Pressable onPress={onAvatarPress}>
            <Avatar {...avatarStyles.container}>
              <Avatar.Image source={{ uri: profileImage }} />
              <Avatar.Fallback
                style={{
                  backgroundColor: "#1570EF",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Text style={{ color: "white" }}>{initials}</Text>
              </Avatar.Fallback>
            </Avatar>
          </Pressable>
        )}
      </XStack>
    </XStack>
  );
}

const stackStyles = {
  container: {
    justifyContent: "space-between",
    alignItems: "center",
  },
  leftSection: {
    alignItems: "center",
    gap: 10,
  },
};

const buttonStyles = {
  backButton: {
    height: 40,
    width: 40,
    borderRadius: 7,
    borderWidth: 1,
    backgroundColor: "$screenBackgroundcolor",
    borderColor: "$primaryBorderColor" as any,
  },
};

const textStyles = {
  screenName: {
    fontSize: 20,
    fontWeight: "600" as any,
  },
  subText: {
    fontSize: 14,
    fontWeight: "400" as any,
    color: "$textcolor" as any,
  },
};

const avatarStyles = {
  container: {
    circular: true,
    size: "$4" as "$4",
    borderColor: "$primaryBorderColor" as any,
  },
  fallback: {
    backgroundColor: "$screenBackgroundcolor" as any,
  },
  messageContainer: {
    justifyContent: "space-between",
    alignItems: "center" as const,
  },
};
