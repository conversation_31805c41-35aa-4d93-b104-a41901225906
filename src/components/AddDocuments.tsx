import { SelectedImage } from "@/nurse/requestVisit";
import { Upload } from "@tamagui/lucide-icons";
import { useMemo } from "react";
import { FlatList } from "react-native";
import { Button, Image, Text, XStack, YStack } from "tamagui";

type AddDocumentsProps = {
  onAddDocument: () => void;
  scannedImages?: SelectedImage[];
  onRemoveImage?: (url: string) => void;
  patientName?: string;
};

export default function AddDocuments({
  onAddDocument,
  scannedImages = [],
  onRemoveImage,
  patientName,
}: AddDocumentsProps) {
  const renderImages = ({ item }: { item: SelectedImage }) => {
    if (!item) return null;

    return (
      <YStack {...styles.imageContainer}>
        <XStack {...styles.imageContainerInner}>
          <Image source={{ uri: item?.url }} {...styles.image} />
          <YStack>
            <Text {...styles.imageText}>Patient: {patientName}</Text>
            <Button
              unstyled
              onPress={() => onRemoveImage?.(item?.url)}
              {...styles.removeImageText}
            >
              Remove
            </Button>
          </YStack>
        </XStack>
      </YStack>
    );
  };

  return (
    <YStack>
      {scannedImages && scannedImages?.length >= 1 && (
        <YStack {...styles.imageList}>
          <FlatList
            data={scannedImages}
            renderItem={renderImages}
            keyExtractor={(item, index) => `${item}-${index}`}
            scrollEnabled={false}
          />
        </YStack>
      )}
      <YStack {...styles.container} borderStyle={"dashed"}>
        <YStack {...styles.subContainer} gap={"$2"}>
          <Upload
            size={"$1.5"}
            onPress={onAddDocument}
            disabled={patientName ? false : true}
          />
          <Text {...styles.attachFileText}> Attach a file </Text>
        </YStack>
      </YStack>
    </YStack>
  );
}

const styles = {
  container: {
    borderWidth: 1,
    borderRadius: 7,
    borderColor: "$primaryBorderColor" as any,
  },
  imageContainer: {
    borderWidth: 1,
    borderRadius: 7,
    borderColor: "$primaryBorderColor" as any,
    backgroundColor: "transparent" as any,
    padding: "$3",
    marginBlockEnd: 10,
  },
  subContainer: {
    marginBlock: 20,
    marginInline: 20,
    justify: "center" as any,
    alignItems: "center" as any,
  },
  attachFileText: {
    fontSize: 15,
    fontWeight: 500 as any,
    color: "$textcolor" as any,
  },
  image: {
    width: 40,
    height: 40,
    resizeMode: "cover" as any,
    borderRadius: 4,
  },
  imageText: {
    fontSize: 14,
    fontWeight: 400 as any,
    color: "$textcolor" as any,
  },
  removeImageText: {
    fontSize: 14,
    fontWeight: 400 as any,
    color: "#FF0000" as any,
  },
  imageList: {
    marginBlockEnd: 10,
  },
  imageContainerInner: {
    gap: "$2" as any,
    alignItems: "center" as any,
  },
};
