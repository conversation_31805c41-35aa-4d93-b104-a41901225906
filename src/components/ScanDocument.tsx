import { SelectedImage } from "@/nurse/requestVisit";
import {
  <PERSON>,
  Check,
  Crop,
  Plus,
  RotateCw,
  Trash,
} from "@tamagui/lucide-icons";
import React, { useEffect, useState } from "react";
import { Dimensions, FlatList, Image as RNImage } from "react-native";
import DocumentScanner, {
  ScanDocumentResponse,
} from "react-native-document-scanner-plugin";
import ImageCropPicker from "react-native-image-crop-picker";
import { Button, styled, Text, XStack, YStack } from "tamagui";

const { width: screenWidth, height: screenHeight } = Dimensions.get("window");

const Container = styled(YStack, {
  flex: 1,
  backgroundColor: "$background",
  justifyContent: "center",
  alignItems: "center",
} as any);

const ImageContainer = styled(YStack, {
  width: screenWidth * 0.9,
  height: screenHeight * 0.7,
  borderRadius: 16,
  elevation: 8,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.3,
  shadowRadius: 12,
  marginHorizontal: 20,
  marginVertical: 40,
  overflow: "hidden",
  alignItems: "center",
  justifyContent: "center",
} as any);

const BottomBar = styled(XStack, {
  paddingHorizontal: 20,
  paddingVertical: 10,
  justifyContent: "space-around",
  alignItems: "center",
  width: "90%",
  position: "absolute",
  bottom: 24,
  left: "5%",
  backgroundColor: "#fff",
  borderRadius: 18,
  zIndex: 10,
  gap: 0,
} as any);

interface ScanDocumentComponentProps {
  open: boolean;
  onClose: (images: SelectedImage[]) => void;
  initialImages?: SelectedImage[];
  maxImages?: number;
}

const ScanDocumentComponent: React.FC<ScanDocumentComponentProps> = ({
  open,
  onClose,
  initialImages = [],
  maxImages = 3,
}) => {
  const styles = useStyles();
  const [scannedImages, setScannedImages] =
    useState<SelectedImage[]>(initialImages);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [rotations, setRotations] = useState<number[]>(
    Array(initialImages.length).fill(0)
  );
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");

  useEffect(() => {
    if (scannedImages.length < maxImages && errorMsg) {
      setErrorMsg("");
    }
  }, [scannedImages.length, maxImages, errorMsg]);

  const scanDocument = async (): Promise<void> => {
    if (scannedImages.length >= maxImages) {
      setErrorMsg(`You can only scan up to ${maxImages} images.`);
      return;
    }
    try {
      const result: ScanDocumentResponse = await DocumentScanner.scanDocument({
        croppedImageQuality: 80,
      });
      if (result.scannedImages && result.scannedImages.length > 0) {
        const newImages = result.scannedImages
          .slice(0, maxImages - scannedImages.length)
          .map((url) => ({
            url,
            info: {
              fileKeys: [],
              fileNames: [],
              fileTypes: [],
            },
          }));
        setScannedImages((prev) => [...prev, ...newImages]);
        setRotations((prev) => [...prev, ...Array(newImages.length).fill(0)]);
        setErrorMsg("");
        if (result.scannedImages.length > newImages.length) {
          setErrorMsg(
            `Only ${maxImages} images allowed. Extra images were ignored.`
          );
        }
      }
    } catch (error) {
      console.error("Document scan failed:", error);
    }
  };

  const handleCrop = async () => {
    try {
      if (!scannedImages[currentIndex]) return;
      const cropped = await ImageCropPicker.openCropper({
        path: scannedImages[currentIndex]?.url,
        width: 300,
        height: 400,
        cropping: true,
        cropperToolbarTitle: "Crop Document",
        mediaType: "photo",
        cropperCircleOverlay: false,
        freeStyleCropEnabled: true,
      });
      if (cropped && cropped.path) {
        setScannedImages((images) =>
          images.map((img, i) =>
            i === currentIndex ? { ...img, url: cropped.path } : img
          )
        );
        setRotations((rot) => rot.map((r, i) => (i === currentIndex ? 0 : r)));
      }
    } catch (error) {
      console.error("Cropping failed:", error);
    }
  };

  const handleRotate = () => {
    setRotations((prev) => {
      const newRotations = [...prev];
      newRotations[currentIndex] =
        ((newRotations[currentIndex] || 0) + 90) % 360;
      return newRotations;
    });
  };

  const handleDelete = () => {
    setScannedImages((images) => images.filter((_, i) => i !== currentIndex));
    setRotations((rot) => rot.filter((_, i) => i !== currentIndex));
    setShowDeleteDialog(false);
    setErrorMsg("");
  };

  if (!open) return null;

  return (
    <Container>
      {errorMsg && scannedImages.length >= maxImages && (
        <YStack {...styles.errorMsgContainer}>
          <RNImage style={styles.hiddenImage} />
          <Text {...styles.errorMsgText}>{errorMsg}</Text>
        </YStack>
      )}
      {scannedImages.length === 0 ? (
        <YStack {...styles.emptyContainer}>
          <Camera size={64} color="#D1D5DB" />
          <Text {...styles.emptyText}>No images captured</Text>
        </YStack>
      ) : (
        <FlatList
          data={scannedImages}
          keyExtractor={(item, index) => `${item}-${index}`}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.flatListContent}
          onMomentumScrollEnd={(e) => {
            const index = Math.round(
              e.nativeEvent.contentOffset.x / screenWidth
            );
            setCurrentIndex(index);
          }}
          renderItem={({ item, index }) => {
            const imageStyle = {
              ...styles.image,
              transform: [{ rotate: `${rotations[index] || 0}deg` }],
            };
            return (
              <ImageContainer onLongPress={() => setShowDeleteDialog(true)}>
                <RNImage
                  source={{ uri: item?.url }}
                  style={imageStyle}
                  resizeMode="contain"
                />
              </ImageContainer>
            );
          }}
        />
      )}
      <BottomBar>
        <YStack {...styles.actionContainer}>
          <Button
            chromeless
            onPress={scanDocument}
            style={
              scannedImages.length >= maxImages
                ? styles.scanButtonDisabled
                : styles.scanButton
            }
            disabled={scannedImages.length >= maxImages}
          >
            <Plus
              size={24}
              color={scannedImages.length >= maxImages ? "#9CA3AF" : "#fff"}
            />
          </Button>
          <Text {...styles.actionLabel}>Scan</Text>
        </YStack>
        <YStack {...styles.actionContainer}>
          <Button chromeless onPress={handleCrop} style={styles.actionButton}>
            <Crop size={22} color="#222" />
          </Button>
          <Text {...styles.actionLabel}>Crop</Text>
        </YStack>
        <YStack {...styles.actionContainer}>
          <Button chromeless onPress={handleRotate} style={styles.actionButton}>
            <RotateCw size={22} color="#222" />
          </Button>
          <Text {...styles.actionLabel}>Rotate</Text>
        </YStack>

        <YStack {...styles.actionContainer}>
          <Button chromeless onPress={handleDelete} style={styles.actionButton}>
            <Trash size={22} color="#EF4444" />
          </Button>
          <Text {...styles.deleteLabel}>Delete</Text>
        </YStack>
        <YStack {...styles.actionContainer}>
          <Button
            chromeless
            onPress={() => onClose(scannedImages)}
            style={styles.actionButton}
          >
            <Check size={22} color="#222" />
          </Button>
          <Text {...styles.actionLabel}>Save</Text>
        </YStack>
      </BottomBar>
    </Container>
  );
};

export default ScanDocumentComponent;

const useStyles = () => {
  return {
    errorMsgContainer: {
      backgroundColor: "rgb(251, 85, 85)",
      padding: 8,
      borderRadius: 5,
      margin: 10,
    },
    hiddenImage: {
      width: 0,
      height: 0,
    },
    errorMsgText: {
      color: "white" as any,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
    emptyText: {
      color: "#D1D5DB" as any,
      fontSize: 18,
      marginTop: 16,
    },
    flatListContent: {
      flexGrow: 1,
      justifyContent: "center" as const,
      alignItems: "center" as const,
    },
    image: {
      width: screenWidth * 0.9,
      height: screenHeight * 0.7,
      borderRadius: 16,
    },
    actionContainer: {
      alignItems: "center",
      flex: 1,
    },
    scanButton: {
      backgroundColor: "#1570EF",
      borderRadius: 100,
      width: 44,
      height: 44,
      alignItems: "center",
      justifyContent: "center",
    },
    scanButtonDisabled: {
      backgroundColor: "#E5E7EB",
      borderRadius: 100,
      width: 44,
      height: 44,
      alignItems: "center",
      justifyContent: "center",
    },
    actionButton: {
      backgroundColor: "#F3F4F6",
      borderRadius: 100,
      width: 44,
      height: 44,
      alignItems: "center",
      justifyContent: "center",
    },
    actionLabel: {
      fontSize: 12,
      color: "#222" as any,
      marginTop: 4,
    },
    deleteLabel: {
      fontSize: 12,
      color: "#EF4444" as any,
      marginTop: 4,
    },
    saveButton: {
      backgroundColor: "#fff",
      borderColor: "#1570EF",
      borderWidth: 1,
      borderRadius: 100,
      width: 44,
      height: 44,
      alignItems: "center",
      justifyContent: "center",
    },
    saveButtonText: {
      color: "#1570EF",
      fontWeight: "bold",
      fontSize: 16,
    },
    saveLabel: {
      fontSize: 12,
      color: "#1570EF",
      marginTop: 4,
    },
  };
};
