import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, TextArea } from "tamagui";
import { Plus } from "@tamagui/lucide-icons";
import { Dimensions } from "react-native";
import { useState } from "react";
import { PatientInfo } from "@/nurse/requestVisit";
import PatientSearchDrawer from "./PatientSearchDrawer";

interface AddpatientProps {
  open: boolean;
  onClose: (open: boolean) => void;
}

const patients = [
  {
    id: "1",
    name: "<PERSON>",
    dob: "1990-01-01",
  },
  {
    id: "2",
    name: "<PERSON>",
    dob: "1990-01-01",
  },
  {
    id: "3",
    name: "<PERSON>",
    dob: "1990-01-01",
  },
  {
    id: "4",
    name: "<PERSON>",
    dob: "1990-01-01",
  },
  {
    id: "5",
    name: "<PERSON>",
    dob: "1990-01-01",
  },
  {
    id: "6",
    name: "<PERSON>",
    dob: "1990-01-01",
  },
  {
    id: "7",
    name: "<PERSON>",
    dob: "1990-01-01",
  },
];

export function Addpatients({ open, onClose }: AddpatientProps): JSX.Element {
  const selectPatient = (id: string, firstName: string, dob: string) => {
    setPatient({ id, name: firstName, dob, facilityId: "" });
  };
  const styles = useAddpatientDialogStyles();
  const [reason, setReason] = useState<string>("");
  const [patient, setPatient] = useState<PatientInfo>({
    id: "",
    name: "",
    dob: "",
    facilityId: "",
  });
  return (
    <Dialog modal open={open} onOpenChange={onClose}>
      <Dialog.Overlay {...styles.overlay} />
      <Dialog.Content {...styles.dialogContent}>
        <YStack {...styles.container}>
          <XStack {...styles.headerContainer}>
            <Text {...styles.headerText}>Add to queue</Text>
          </XStack>
          <YStack {...styles.body}>
            <Text {...styles.patientText}>Patient</Text>
            <YStack {...styles.patientDrawer}>
              <PatientSearchDrawer
                data={patients || []}
                placeholder="Select a Patient"
                onSelect={(id: string, firstName: string, dob: string) =>
                  selectPatient(id, firstName, dob)
                }
                onSearch={(query: string) => {
                  return Promise.resolve();
                }}
                disabled={false}
                loading={false}
                error={""}
              />
            </YStack>

            {patient && patient?.id !== "" && (
              <YStack marginBlock={10}>
                <Text {...styles.chiefComplaintText}>Chief complaint</Text>
                <TextArea
                  {...styles.complaintTextArea}
                  placeholder="Please enter the details."
                  placeholderTextColor={"$textcolor"}
                  overflow="hidden"
                  value={reason}
                  onChangeText={setReason}
                />
              </YStack>
            )}

            <Button
              onPress={() => onClose(false)}
              icon={<Plus size={"$1"} color={"white"} />}
              {...styles.addBtn}
            >
              Add
            </Button>
          </YStack>
        </YStack>
        <YStack></YStack>
      </Dialog.Content>
    </Dialog>
  );
}

export const useAddpatientDialogStyles = () => {
  const screenHeight = Dimensions.get("window").height;
  const isSmallScreen = screenHeight < 700;

  return {
    dialogContent: {
      bordered: true,
      bg: "$screenBackgroundcolor" as any,
      br: "$4" as any,
      shadowColor: "transparent" as any,
      shadowOpacity: 0 as any,
      shadowRadius: 0 as any,
      width: "90%" as any,
      alignSelf: "center" as any,
      justifyContent: "center" as any,
      borderRadius: "$7" as any,
      position: "absolute" as any,
      top: "50%" as any,
      left: "50%" as any,
      transform: [
        { translateX: "-50%" },
        { translateY: isSmallScreen ? "-75%" : "-50%" },
      ] as any,
      borderColor: "$primaryBorderColor" as any,
    },
    container: {
      gap: "$2" as any,
    },
    headerContainer: {
      justifyContent: "space-between",
    },
    headerText: {
      fontSize: 24,
      fontWeight: "600" as any,
    },
    overlay: {
      animation: "quick" as any,
      enterStyle: { opacity: 0 },
      exitStyle: { opacity: 0 },
      backgroundColor: "black" as any,
      opacity: 0.6,
    },
    body: {
      marginBlockStart: 10,
    },
    patientText: {
      fontWeight: 500 as any,
      fontSize: 14,
    },
    patientDrawer: {
      marginBlockStart: 10,
      marginBlockEnd: 60,
    },
    addBtn: {
      marginBlockStart: 10,
      fontSize: 16,
      fontWeight: 600 as any,
      backgroundColor: "$primaryColor",
      color: "white" as any,
    },
    complaintTextArea: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
      size: "$1" as "$1",
      borderWidth: 1,
      borderRadius: 7,
      color: "$textcolor" as any,
      fontWeight: 500 as any,
      padding: 10,
      fontSize: 16,
      numberOfLines: 6,
      textAlignVertical: "top" as any,
    },
    chiefComplaintText: {
      fontWeight: 500 as any,
      fontSize: 14,
      marginBlockEnd: 10,
    },
  };
};
