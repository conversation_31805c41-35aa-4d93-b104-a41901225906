import React, { useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  Image as RNImage,
  Text as RNText,
  TouchableOpacity,
} from "react-native";
import ImageViewing from "react-native-image-viewing";
import { Text, View, XStack, YStack } from "tamagui";

export interface Attachment {
  url: string;
  id?: string | number;
}

interface AttachmentSectionProps {
  attachments: Attachment[];
  containerStyle?: any;
  imageContainerStyle?: any;
  emptyText?: string;
}

const AttachmentSection: React.FC<AttachmentSectionProps> = ({
  attachments = [],
  containerStyle = {},
  imageContainerStyle = {},
  emptyText = "No attachments",
}) => {
  const styles = useStyles();
  const [imageLoaded, setImageLoaded] = useState<{ [uri: string]: boolean }>(
    {}
  );
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const handleImageLoad = (uri: string) => {
    setImageLoaded((prev) => ({ ...prev, [uri]: true }));
  };

  return (
    <YStack {...styles.container}>
      <Text {...styles.attachmentsText}>Attachments</Text>
      <XStack {...styles.xStack}>
        <FlatList
          data={attachments}
          horizontal
          keyExtractor={(item, index) =>
            item.id?.toString() || item.url || index.toString()
          }
          renderItem={({ item }) => (
            <AttachmentImage
              uri={item.url}
              loaded={!!imageLoaded[item.url]}
              onLoad={handleImageLoad}
              onPress={() => {
                setSelectedImage(item.url);
                setModalVisible(true);
              }}
              imageContainerStyle={imageContainerStyle}
            />
          )}
          ListEmptyComponent={
            <RNText style={styles.emptyText}>{emptyText}</RNText>
          }
        />
      </XStack>
      {modalVisible && selectedImage && (
        <ImageViewing
          key={selectedImage}
          images={[{ uri: selectedImage }]}
          imageIndex={0}
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}
        />
      )}
    </YStack>
  );
};

const AttachmentImage = ({
  uri,
  loaded,
  onLoad,
  onPress,
  imageContainerStyle = {},
}: {
  uri: string;
  loaded: boolean;
  onLoad: (uri: string) => void;
  onPress?: () => void;
  imageContainerStyle?: any;
}) => {
  const styles = useStyles();
  return (
    <View style={{ ...styles.imageContainer, ...imageContainerStyle }}>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={onPress}
        style={styles.touchable}
      >
        <RNImage
          source={{ uri }}
          style={styles.image}
          onLoadEnd={() => onLoad(uri)}
        />
        {!loaded && (
          <View {...styles.loadingOverlay}>
            <ActivityIndicator size="small" color="#1570EF" />
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
};

const useStyles = () => ({
  container: {
    marginBlockStart: 10,
    marginInline: 0,
    borderColor: "$primaryBorderColor" as any,
    borderWidth: 1,
    borderRadius: 10,
    backgroundColor: "$screenBackgroundcolor" as any,
    padding: 10,
  },
  attachmentsText: {
    fontSize: 16,
    fontWeight: "600" as any,
    marginBottom: 10,
  },
  xStack: {
    marginHorizontal: 10,
  },
  emptyText: {
    color: "#888",
    fontSize: 12,
  },
  imageContainer: {
    marginRight: 10,
    width: 60,
    height: 60,
  },
  touchable: {
    width: 60,
    height: 60,
    borderRadius: 6,
    overflow: "hidden" as any,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: 6,
  },
  loadingOverlay: {
    position: "absolute" as const,
    top: 0,
    left: 0,
    width: 60,
    height: 60,
    borderRadius: 6,
    borderWidth: 1,
    backgroundColor: "transparent",
    alignItems: "center" as any,
    justifyContent: "center" as any,
    borderColor: "$primaryBorderColor" as any,
  },
});

export default AttachmentSection;
