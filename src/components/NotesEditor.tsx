import { useTheme } from "@/_layout";
import { ChevronDown } from "@tamagui/lucide-icons";
import React, { forwardRef, useEffect, useState } from "react";
import {
  Accordion,
  Square,
  Text,
  TextArea,
  View,
  XStack,
  YStack,
} from "tamagui";

interface NotesEditorProps {
  title: string;
  subtitle: string;
  onChangeText?: (text: string) => void;
  data: string;
}

const NotesEditor = forwardRef<any, NotesEditorProps>(
  ({ title, subtitle, onChangeText, data }, ref) => {
    const [isOpen, setIsOpen] = useState(true);
    const [inputValue, setInputValue] = useState("");
    const styles = getStyles();
    const { theme } = useTheme();
    const isDarkMode = theme === "dark";

    useEffect(() => {
      setInputValue(data);
    }, [data]);

    const handleTextChange = (text: string) => {
      setInputValue(text);
      onChangeText?.(text);
    };

    const handleAccordionPress = () => {
      setIsOpen((prev) => !prev);
    };

    return (
      <View ref={ref} {...styles.container}>
        <Accordion
          overflow="hidden"
          borderWidth={1}
          type="multiple"
          value={isOpen ? ["a1"] : []}
          onValueChange={(val) => setIsOpen(val.includes("a1"))}
          {...styles.accordion}
          borderColor={isDarkMode ? "#697586" : "#D0D5DD"}
        >
          <Accordion.Item value="a1">
            <Accordion.Trigger
              {...styles.trigger}
              onPress={handleAccordionPress}
            >
              {() => (
                <XStack {...styles.headerRow}>
                  <YStack {...styles.titleTextarea}>
                    <YStack>
                      <Text {...styles.headerText}>{title}</Text>
                      <Text {...styles.subtitleText}>{subtitle}</Text>
                    </YStack>
                  </YStack>
                  <Square animation="quick" rotate={isOpen ? "180deg" : "0deg"}>
                    <ChevronDown size="$1" />
                  </Square>
                </XStack>
              )}
            </Accordion.Trigger>
            <Accordion.HeightAnimator animation="medium">
              {isOpen && (
                <Accordion.Content {...styles.content}>
                  <YStack {...styles.innerContainer}>
                    <TextArea
                      {...styles.inputText}
                      value={inputValue ?? data}
                      onChangeText={handleTextChange}
                      placeholder={"Please enter the details."}
                      placeholderTextColor={"$textcolor"}
                      overflow="hidden"
                    />
                  </YStack>
                </Accordion.Content>
              )}
            </Accordion.HeightAnimator>
          </Accordion.Item>
        </Accordion>
      </View>
    );
  }
);

export default NotesEditor;
const getStyles = () => {
  return {
    container: {
      marginBlockStart: 10,
    },
    accordion: {
      borderRadius: "$6" as "$6",
    },
    trigger: {
      flexDirection: "row" as "row",
      borderWidth: 0,
      backgroundColor: "$screenBackgroundcolor",
    },
    headerRow: {
      justifyContent: "space-between",
      flex: 1,
    },
    content: {
      animation: "medium" as "medium",
      exitStyle: { opacity: 0 },
      backgroundColor: "$screenBackgroundcolor",
    },
    innerContainer: {
      marginBlockStart: -40,
      marginInlineStart: 0,
    },
    headerText: {
      fontSize: 16,
      fontWeight: 600 as any,
    },
    subtitleText: {
      marginBlockStart: 5,
      fontSize: 12,
      fontWeight: 400 as any,
      color: "$soapNotesSubtitleText" as any,
    },
    inputText: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderWidth: 1,
      color: "$textcolor" as any,
      size: "$3" as any,
      textAlignVertical: "top" as any,
      borderColor: "$primaryBorderColor" as any,
      marginBlockStart: 15,
      bordeWidth: 1,
      padding: 10,
      numberOfLines: 7,
      borderRadius: 7,
    },
    orderConfirmText: {
      color: "$textcolor" as any,
      fontWeight: 300 as 300,
      fontSize: 14,
      marginInline: 10,
      maxWidth: "100%",
    },
    titleTextarea: {
      maxWidth: "90%" as any,
    },
  };
};
