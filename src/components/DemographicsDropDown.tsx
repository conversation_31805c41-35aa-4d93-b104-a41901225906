import { useTheme } from "@/_layout";
import { ChevronDown } from "@tamagui/lucide-icons";
import { useState } from "react";
import { PatientDetailsSnapshot } from "src/types/patientData";
import { Accordion, Square, Text, View, XStack, YStack } from "tamagui";

interface DemographicsDropDownProps {
  title: string;
  data: PatientDetailsSnapshot | undefined;
}

export default function DemographicsDropDown({
  title,
  data,
}: DemographicsDropDownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const styles = getStyles();
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";

  return (
    <View {...styles.container}>
      <Accordion
        overflow="hidden"
        borderWidth={1}
        type="multiple"
        {...styles.accordion}
        borderColor={isDarkMode ? "#697586" : "#D0D5DD"}
      >
        <Accordion.Item value="a1">
          <Accordion.Trigger
            {...styles.trigger}
            onPress={() => setIsOpen(!isOpen)}
          >
            {({ open }: { open: boolean }) => (
              <XStack {...styles.headerRow}>
                <YStack>
                  <Text {...styles.headerText}>{title}</Text>
                </YStack>
                <Square animation="quick" rotate={open ? "180deg" : "0deg"}>
                  <ChevronDown size="$1" />
                </Square>
              </XStack>
            )}
          </Accordion.Trigger>
          <Accordion.HeightAnimator animation="medium">
            <Accordion.Content {...styles.content}>
              <YStack {...styles.innerContainer}>
                {data && (
                  <>
                    <YStack>
                      <Text {...styles.sectionTitle}>DOB:</Text>
                      <Text {...styles.sectionText}>{data.dateOfBirth}</Text>
                    </YStack>
                    <YStack {...styles.section}>
                      <Text {...styles.sectionTitle}>Race:</Text>
                      <Text {...styles.sectionText}>{data.race}</Text>
                    </YStack>
                  </>
                )}
              </YStack>
            </Accordion.Content>
          </Accordion.HeightAnimator>
        </Accordion.Item>
      </Accordion>
    </View>
  );
}

const getStyles = () => {
  return {
    container: {
      marginBlockStart: 10,
    },
    accordion: {
      borderRadius: "$6" as "$6",
    },
    trigger: {
      flexDirection: "row" as "row",
      borderWidth: 0,
      backgroundColor: "$screenBackgroundcolor",
    },
    headerRow: {
      justifyContent: "space-between",
      flex: 1,
    },
    content: {
      animation: "medium" as "medium",
      exitStyle: { opacity: 0 },
      backgroundColor: "$screenBackgroundcolor",
    },
    innerContainer: {
      marginBlockStart: -15,
    },
    headerText: {
      fontSize: 16,
      fontWeight: 600 as any,
    },

    section: {
      marginBlockStart: 15,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: "400" as "400",
      color: "$textcolor" as any,
    },
    sectionText: {
      fontSize: 16,
      fontWeight: "300" as "300",
      marginBlockStart: 7,
      color: "$textcolor" as any,
    },
  };
};
