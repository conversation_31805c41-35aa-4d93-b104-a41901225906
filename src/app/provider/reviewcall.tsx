import { CommonActions } from "@react-navigation/native";
import { HelpCir<PERSON>, PenLine, Save } from "@tamagui/lucide-icons";
import { useLocalSearchParams, useNavigation, useRouter } from "expo-router";
import { default as React, ReactNode, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Keyboard,
  Modal,
  TouchableOpacity,
} from "react-native";
import BillableToggle from "src/components/BillableToggle";
import { ContentDialog } from "src/components/ContentDialog";
import { DialogBox } from "src/components/Dialog";
import ScreenHeader from "src/components/ScreenHeader";
import SheetDemo from "src/components/SettingsDrawer";
import { setShouldRefetchConsultations } from "src/globals/consultationFlag";
import { useConsultationV2 } from "src/hooks/useCOnsultationV2";
import { PhysicalExam } from "src/types/consultationV2";
import { Button, Image, Spinner, Text, View, XStack, YStack } from "tamagui";
import axiosConfig from "~/services/axiosConfig";
import Billing from "./billing";
import Profile from "./profile";
import SOAP from "./soap";
import { useReviewCallStyles } from "./Styles/ReviewCallStyle";
import Visit from "./Visit";

export const escapeRegex = (str: string): string =>
  str.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");

export default function ReviewCall() {
  const { consultationId } = useLocalSearchParams();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const reviewCallStyles = useReviewCallStyles();
  const navigation = useNavigation();
  const [contentDialog, setContentDialog] = useState<{
    title: string;
    body: ReactNode;
    btnText: string;
  }>({
    title: "",
    body: null,
    btnText: "Close",
  });
  const [contentDialogOpen, setContentDialogOpen] = useState(false);
  const [notes, setNotes] = useState("");
  const [consultationNotes, setConsultationNotes] = useState("");
  const [consultationOrder, setConsultationOrder] = useState("");
  const [generatingSummary, setGeneratingSummary] = useState(false);
  const [
    ConsultationsSelectedBillingCodes,
    setConsultationsSelectedBillingCodes,
  ] = useState<{ code: string; description: string }[]>([]);
  const [ordersText, setOrdersText] = useState("");
  const [selectedCodes, setSelectedCodes] = useState<
    { code: string; description: string }[]
  >([]);
  const [consultationsSelectedCodes, setConsultationsSelectedCodes] = useState<
    { code: string; description: string }[]
  >([]);
  const [selectedBillingCodes, setSelectedBillingCodes] = useState<
    { code: string; description: string }[]
  >([]);
  const [patientDetailsSnapshot, setPatientDetailsSnapshot] =
    useState<any>(null);
  const [isClicked, setIsClicked] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [generateSummary, setGenerateSummary] = useState(false);
  const [dialogContent, setDialogContent] = useState<{
    title: string;
    body: ReactNode | string;
    btnText: string;
  }>({
    title: "",
    body: "",
    btnText: "Close",
  });
  const [isSubmitted, setSubmitted] = useState(false);
  const [physicalExam, setPhysicalExam] = useState<PhysicalExam | null>(null);
  const [isBillable, setIsBillable] = useState(true);
  const [pendingCode, setPendingCode] = useState<{
    code: string;
    description: string;
  } | null>(null);
  const [subjective, setSubjective] = useState("");
  const [objective, setObjective] = useState("");
  const [assessment, setAssessment] = useState("");
  const [plan, setPlan] = useState("");
  const [aiBillingNote, setAIBillingNote] = useState("");
  const { consultation, loading, error, refresh } = useConsultationV2(
    consultationId as string
  );
  const [reviewConsultation, setReviewConsultation] = useState<any>(null);
  useEffect(() => {
    console.log("ordersText ", ordersText);
  }, [ordersText]);

  useEffect(() => {
    if (consultation) {
      setSubmitted(consultation.status === "submitted");
      setIsBillable(consultation.status === "non_billable" ? false : true);
      setOrdersText(consultation.order || "");
      setConsultationOrder(consultation?.order || "");
      setPatientDetailsSnapshot(consultation.patient_details_snapshot || {});
      setNotes(consultation.notes || "");
      setConsultationNotes(consultation.notes || "");
      setSubjective(consultation?.ai_generated_note_subjective || "");
      setObjective(consultation?.ai_generated_note_objective || "");
      setAssessment(consultation?.ai_generated_note_assessment || "");
      setPlan(consultation?.ai_generated_note_plan || "");
      setAIBillingNote(consultation?.ai_billing_note || "");
      if (consultation.billing_codes) {
        setSelectedBillingCodes(consultation.billing_codes);
        setConsultationsSelectedBillingCodes(consultation.billing_codes);
      }
      if (consultation.icd_codes) {
        setSelectedCodes(consultation.icd_codes);
        setConsultationsSelectedCodes(consultation.icd_codes);
      }
    }
    if (consultation?.physical_exam) {
      setPhysicalExam(consultation.physical_exam);
    }
    const time = new Date(consultation?.created_at || "").toLocaleTimeString(
      [],
      {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: true,
      }
    );

    const date = new Date(consultation?.created_at || "").toLocaleDateString();

    const tempConsultation: any = {
      id: consultation?.id || "",
      full_name: `${consultation?.patient_details_snapshot?.full_name}`,
      gender: consultation?.patient_details_snapshot?.gender || "",
      dob: consultation?.patient_details_snapshot?.dateOfBirth || "",
      date: date,
      time: time,
      chief_complaint: consultation?.chief_complaint || "",
      order: consultation?.order || "",
      status: consultation?.status || "",
      provider_name: consultation?.provider_name || "",
    };
    setReviewConsultation(tempConsultation);
  }, [consultation]);

  const patientName =
    patientDetailsSnapshot?.name ||
    `${patientDetailsSnapshot?.firstName || ""} ${patientDetailsSnapshot?.lastName || ""}`;

  const generateAISummary = async () => {
    const missingFields: string[] = [];
    // if (!consultationNotes || consultationNotes.trim() === "") {
    //   missingFields.push("notes");
    // }
    if (!consultationOrder || consultationOrder.trim() === "") {
      missingFields.push("order");
    }
    if (
      !ConsultationsSelectedBillingCodes ||
      ConsultationsSelectedBillingCodes.length === 0
    ) {
      missingFields.push("billing codes");
    }
    if (
      !consultationsSelectedCodes ||
      consultationsSelectedCodes.length === 0
    ) {
      missingFields.push("ICD codes");
    }
    if (missingFields.length > 0) {
      const fieldList = missingFields
        .join(", ")
        .replace(/, ([^,]*)$/, " and $1");
      setDialogOpen(false);
      setTimeout(() => {
        setDialogContent({
          title: "Required Fields Missing",
          body: (
            <YStack>
              <Text>
                The following required field
                {missingFields.length > 1 ? "s are" : " is"} missing:
              </Text>
              <YStack mt="$2" ml="$3">
                {missingFields.map((field, index) => (
                  <Text key={index}>• {field}</Text>
                ))}
              </YStack>
              <Text mt="$2">
                Please add and save them before generating the AI summary.
              </Text>
            </YStack>
          ),
          btnText: "OK",
        });
        setDialogOpen(true);
      }, 200);
      return;
    }
    try {
      setGeneratingSummary(true);
      const response = await axiosConfig.post("/billing/ai-summary-generate", {
        consultationId: consultationId,
      });
      if (response.status === 200) {
        const { aiBillingNote } = response.data;
        setSubjective(aiBillingNote?.subjective || "");
        setObjective(aiBillingNote?.objective || "");
        setAssessment(aiBillingNote?.assessment || "");
        setPlan(aiBillingNote?.plan || "");
        setGeneratingSummary(false);
      }
      setIsClicked(true);
      setDialogContent({
        title: "AI Summary Generated",
        body: "The AI-generated SOAP note was created successfully.",
        btnText: "OK",
      });
      setDialogOpen(true);
    } catch (error: any) {
      const errorDetails = error?.response?.data?.details;
      setDialogContent({
        title: "Error generating summary",
        body:
          errorDetails + ". Update the Required fields and try again later. " ||
          "Please try again.",
        btnText: "OK",
      });
      setDialogOpen(true);
      setGeneratingSummary(false);
    } finally {
      setGeneratingSummary(false);
    }
  };

  const handleUnavailableCode = (code: {
    code: string;
    description: string;
  }) => {
    const establishedPatientMessage = `${patientName} has been seen before. This code (${code.code}) is typically used for new patients. Are you sure you want to select it?`;
    const newPatientMessage = `${patientName} has not been seen before. This code (${code.code}) is typically used for established patients. Are you sure you want to select it?`;
    setPendingCode(code);
    setDialogContent({
      title: "Warning: Verify Code",
      body: consultation?.isEstablishedPatient
        ? establishedPatientMessage
        : newPatientMessage,
      btnText: "Add Code",
    });
    setDialogOpen(true);
  };

  const onFinishLaterDialog = () => {
    if (dialogContent?.title === "Success") {
      refresh();
    } else if (dialogContent?.title === "Warning: Verify Code" && pendingCode) {
      setSelectedBillingCodes([pendingCode]);
      setPendingCode(null);
    } else if (dialogContent?.title === "Unsaved Changes") {
      generateAISummary();
    }
    setDialogOpen(false);
  };

  const clearStackAndMove = () => {
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: "dashboard" }],
      })
    );
  };

  const validateBeforeSubmit = () => {
    if (!ordersText.trim()) {
      setDialogContent({
        title: "Missing Orders",
        body: "Please add at least one order before submitting.",
        btnText: "Add Order",
      });
      setDialogOpen(true);
      return false;
    }
    // if (!notes.trim()) {
    //   setDialogContent({
    //     title: "Visit Notes Required",
    //     body: "All visit notes must be completed before submitting for billing.",
    //     btnText: "Add Notes",
    //   });
    //   setDialogOpen(true);
    //   return false;
    // }
    if (!selectedBillingCodes.length) {
      setDialogContent({
        title: "Billing Codes Required",
        body: "Please add the necessary billing codes.",
        btnText: "Add Codes",
      });
      setDialogOpen(true);
      return false;
    }
    if (!selectedCodes.length) {
      setDialogContent({
        title: "ICD Codes Required",
        body: "Please add the necessary ICD codes.",
        btnText: "Add Codes",
      });
      setDialogOpen(true);
      return false;
    }
    const allStartWithW = selectedCodes.every((codeObj) =>
      codeObj.code.toUpperCase().startsWith("W")
    );
    if (allStartWithW) {
      setDialogContent({
        title: "Another ICD code required",
        body: "Please add the one or more ICD codes that does not start with W.",
        btnText: "Add Codes",
      });
      setDialogOpen(true);
      return false;
    }
    if (!subjective.trim()) {
      setDialogContent({
        title: "Subjective Required",
        body: "Please add the necessary subjective.",
        btnText: "Add Subjective",
      });
      setDialogOpen(true);
      return false;
    }
    if (!objective.trim()) {
      setDialogContent({
        title: "Objective Required",
        body: "Please add the necessary objective.",
        btnText: "Add Objective",
      });
      setDialogOpen(true);
      return false;
    }
    if (!assessment.trim()) {
      setDialogContent({
        title: "Assessment Required",
        body: "Please add the necessary assessment.",
        btnText: "Add Assessment",
      });
      setDialogOpen(true);
      return false;
    }
    if (!plan.trim()) {
      setDialogContent({
        title: "Plan Required",
        body: "Please add the necessary plan.",
        btnText: "Add Plan",
      });
      setDialogOpen(true);
      return false;
    }

    return true;
  };

  const saveConsultation = async () => {
    try {
      setIsSaving(true);
      setTimeout(async () => {
        try {
          const SUBJECTIVE = subjective;
          const OBJECTIVE = objective;
          const ASSESMENT = assessment;
          const consultationStatus = !isBillable ? "non_billable" : "completed";
          await axiosConfig.put(`/consultation/${consultationId}`, {
            order: ordersText,
            notes,
            billingCodes: selectedBillingCodes,
            icdCodes: selectedCodes,
            patientDetails: patientDetailsSnapshot,
            physical_exam: physicalExam,
            status: consultationStatus,
            ai_generated_note_objective: OBJECTIVE,
            ai_generated_note_assessment: ASSESMENT,
            ai_generated_note_subjective: SUBJECTIVE,
            ai_generated_note_plan: plan,
          });
          setShouldRefetchConsultations(true);
          setDialogContent({
            title: "Success",
            body: "Consultation Saved SuccessFully",
            btnText: "OK",
          });
          setDialogOpen(true);
        } catch (error) {
          setDialogContent({
            title: "Error Saving Consultation",
            body: "Consultation not saved Please try again later",
            btnText: "OK",
          });
          setDialogOpen(true);
        } finally {
          setIsSaving(false);
        }
      }, 100);
    } catch (err) {
      setDialogContent({
        title: "Error Saving Consultation",
        body: "Consultation not saved Please try again later",
        btnText: "OK",
      });
      setDialogOpen(true);
      setIsSaving(false);
    }
  };

  const submitConsultation = async () => {
    if (!isBillable) {
      saveConsultation();
      return;
    }
    if (!validateBeforeSubmit()) return;
    setSubmitted(true);
    try {
      setIsSaving(true);
      setTimeout(async () => {
        try {
          const consultationStatus = !isBillable ? "non-billable" : "submitted";
          try {
            const SUBJECTIVE = subjective;
            const OBJECTIVE = objective;
            const ASSESMENT = assessment;
            await axiosConfig.put(`/consultation/${consultationId}`, {
              order: ordersText,
              notes,
              billingCodes: selectedBillingCodes,
              icdCodes: selectedCodes,
              patientDetails: patientDetailsSnapshot,
              physical_exam: physicalExam,
              status: consultationStatus,
              ai_generated_note_objective: OBJECTIVE,
              ai_generated_note_assessment: ASSESMENT,
              ai_generated_note_subjective: SUBJECTIVE,
              ai_generated_note_plan: plan,
            });

            setShouldRefetchConsultations(true);
          } catch (err) {
            setDialogContent({
              title: "Error Saving Consultation",
              body: "Consultation not saved Please try again later",
              btnText: "OK",
            });
            setDialogOpen(true);
          }
          setShouldRefetchConsultations(true);
          // clearStackAndMove();
        } catch (error) {
          console.error("Error submitting consultation:", error);
        } finally {
          setIsSaving(false);
        }
      }, 500);
    } catch (err) {
      console.error("Unexpected error:", err);
      setIsSaving(false);
    }
  };
  const showSOAPDialogue = () => {
    setContentDialogOpen(true);
    setContentDialog({
      title: "How Our AI-Generated SOAP Notes Work",
      body: (
        <YStack>
          <YStack>
            <Text>
              <Text fontWeight="bold">1. We gather the data</Text>
            </Text>
            <Text ml="$4">
              • Billing codes{"\n"}• Order{"\n"}• Call transcripts{"\n"}•
              Available medical history{"\n"}• Notes you add during or after
              visit
            </Text>
          </YStack>
          <YStack mt="$3">
            <Text>
              <Text fontWeight="bold">2. AI drafts your note</Text>
            </Text>
            <Text ml="$4">
              Our intelligent engine analyzes everything above to create a
              complete S-O-A-P note.
            </Text>
          </YStack>
          <YStack mt="$3">
            <Text>
              <Text fontWeight="bold">3. You review & finalize</Text>
            </Text>
            <Text ml="$4">
              Tweak any details before signing off—so you're always in control.
            </Text>
          </YStack>
        </YStack>
      ),
      btnText: "Close",
    });
  };
  const openSettings = () => setOpen(true);

  const navigateBack = () => {
    router.back();
  };

  const tabs = ["Profile", "Visit", "Billing", "SOAP"] as const;
  const [activeTab, setActiveTab] = useState<
    "Profile" | "Visit" | "Billing" | "SOAP"
  >("Profile");

  const renderTab = (tab: (typeof tabs)[number]) => (
    <TouchableOpacity
      key={tab}
      style={[
        reviewCallStyles.tab,
        activeTab === tab && reviewCallStyles.activeTab,
      ]}
      onPress={() => setActiveTab(tab)}
    >
      <Text
        style={[
          reviewCallStyles.tabText,
          activeTab === tab && reviewCallStyles.activeTabText,
        ]}
      >
        {tab}
      </Text>
    </TouchableOpacity>
  );

  const areCodeArraysEqual = (
    arr1: { code: string; description: string }[],
    arr2: { code: string; description: string }[]
  ) => {
    if (arr1.length !== arr2.length) return false;
    const sorted1 = [...arr1].sort((a, b) => a.code.localeCompare(b.code));
    const sorted2 = [...arr2].sort((a, b) => a.code.localeCompare(b.code));
    return sorted1.every(
      (item, idx) =>
        item.code === sorted2[idx].code &&
        item.description === sorted2[idx].description
    );
  };

  if (loading || isSaving) {
    return (
      <View {...reviewCallStyles.container}>
        <YStack {...reviewCallStyles.centeredContent}>
          <Spinner size="large" />
        </YStack>
      </View>
    );
  }

  if (error) {
    return (
      <View {...reviewCallStyles.container}>
        <YStack {...reviewCallStyles.mainStack}>
          <ScreenHeader
            onAvatarPress={openSettings}
            screenName="Back"
            onBackPress={navigateBack}
            shouldNavibgateToChat={true}
            consultationId={consultationId as string}
          />
          <YStack {...reviewCallStyles.centeredContent}>
            <Text fontSize={20} color="$textcolor">
              Error fetching consultation data.
            </Text>
          </YStack>
        </YStack>
      </View>
    );
  }

  return (
    <View {...reviewCallStyles.container}>
      <YStack {...reviewCallStyles.mainStack}>
        <ScreenHeader
          onAvatarPress={openSettings}
          screenName={patientName}
          subText={`Nurse: ${consultation?.nurse_name || "N/A"}`}
          onBackPress={navigateBack}
          shouldNavibgateToChat={true}
          consultationId={consultationId as string}
        />
        <YStack {...reviewCallStyles.tabLayout}>
          <View>
            <View {...reviewCallStyles.tabContainer}>
              {tabs.map((tab, index) => (
                <React.Fragment key={tab}>
                  {renderTab(tab)}
                  {index < tabs.length - 1 && (
                    <View {...reviewCallStyles.divider} />
                  )}
                </React.Fragment>
              ))}
            </View>
          </View>
        </YStack>
        <YStack flex={1}>
          {activeTab === "Visit" && (
            <YStack flex={1}>
              <Visit
                notes={notes}
                setNotes={setNotes}
                orderText={ordersText}
                onOrdersChange={setOrdersText}
                isSubmitted={isSubmitted}
                consultation={consultation}
                physicalExam={physicalExam}
                setPhysicalExam={setPhysicalExam}
              />
            </YStack>
          )}
          {activeTab === "Billing" && (
            <YStack flex={1}>
              <Billing
                selectedCodes={selectedCodes}
                setSelectedCodes={setSelectedCodes}
                selectedBillingCodes={selectedBillingCodes}
                setSelectedBillingCodes={setSelectedBillingCodes}
                isSubmitted={isSubmitted}
                isEstablishedPatient={
                  consultation?.isEstablishedPatient ?? false
                }
                onUnavailableCode={handleUnavailableCode}
              />
            </YStack>
          )}
          {activeTab === "Profile" && (
            <YStack flex={1}>
              <Profile consultation={consultation} />
            </YStack>
          )}
          {activeTab === "SOAP" && (
            <YStack flex={1}>
              <XStack
                justify="space-between"
                verticalAlign="center"
                width="100%"
              >
                <YStack flex={1}>
                  <Text {...reviewCallStyles.billingNotesText}>
                    Billing Notes
                  </Text>
                </YStack>
                <XStack
                  flex={1}
                  justify="flex-end"
                  verticalAlign="center"
                  gap={8}
                >
                  <Button
                    disabled={isSubmitted}
                    {...reviewCallStyles.generateSummaryBtn}
                    icon={
                      <Image
                        source={require("../../assets/images/star.png")}
                        {...reviewCallStyles.starIcon}
                      />
                    }
                    width="auto"
                    onPress={() => {
                      Keyboard.dismiss();
                      if (
                        notes !== consultationNotes ||
                        !areCodeArraysEqual(
                          selectedBillingCodes,
                          ConsultationsSelectedBillingCodes
                        ) ||
                        !areCodeArraysEqual(
                          selectedCodes,
                          consultationsSelectedCodes
                        ) ||
                        ordersText !== consultationOrder
                      ) {
                        setDialogContent({
                          title: "Unsaved Changes",
                          body: "You have unsaved changes. Please save your changes before generating the AI summary, If unsaved Ai summary will be based on previous notes biiling codes and icd codes. Continue anyway?",
                          btnText: "OK",
                        });
                        setDialogOpen(true);
                        return;
                      }
                      generateAISummary();
                    }}
                  >
                    Generate Summary
                  </Button>
                  <HelpCircle
                    color="#888"
                    size={24}
                    onPress={showSOAPDialogue}
                  />
                </XStack>
              </XStack>
              <SOAP
                subjective={subjective}
                setSubjective={setSubjective}
                objective={objective}
                setObjective={setObjective}
                assessment={assessment}
                setAssessment={setAssessment}
                plan={plan}
                setPlan={setPlan}
                isSubmitted={isSubmitted}
                aiBillingNote={aiBillingNote}
              />
            </YStack>
          )}
        </YStack>
        {(activeTab === "Visit" ||
          activeTab === "Billing" ||
          activeTab === "SOAP") &&
          !isSubmitted && (
            <YStack {...reviewCallStyles.signInAndSaveContainer}>
              <Button
                {...reviewCallStyles.saveBtn}
                icon={<Save size={"$1"} />}
                onPress={saveConsultation}
              >
                Save
              </Button>
              {activeTab !== "Visit" && (
                <XStack {...reviewCallStyles.buttonRow} gap="$1">
                  <View flex={1}>
                    <BillableToggle
                      isBillable={isBillable}
                      onToggle={setIsBillable}
                      disabled={isSubmitted}
                    />
                  </View>
                  <View flex={3}>
                    <Button
                      {...reviewCallStyles.signInAndSaveBtn}
                      icon={<PenLine size={"$1"} />}
                      onPress={submitConsultation}
                      width="100%"
                    >
                      {isBillable ? "Sign & Submit" : "Submit"}
                    </Button>
                  </View>
                </XStack>
              )}
            </YStack>
          )}
        <Modal visible={dialogOpen} transparent animationType="fade">
          <View
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              width: "100%",
              height: "100%",
            }}
          >
            <DialogBox
              open={dialogOpen}
              onClose={setDialogOpen}
              title={dialogContent.title}
              body={dialogContent.body}
              btnText={dialogContent.btnText}
              onFinishLater={onFinishLaterDialog}
              showCloseBtn={
                dialogContent.title === "Generate AI Summary"
                  ? true
                  : ![
                      "Error generating summary",
                      "Error Saving Consultation",
                      "Success",
                      "How Our AI-Generated SOAP Notes Work",
                      "Required Fields Missing",
                      "Assessment Required",
                      "Plan Required",
                      "Objective Required",
                      "Subjective Required",
                    ].includes(dialogContent.title)
              }
            />
          </View>
        </Modal>
        {open && <SheetDemo open={open} setOpen={setOpen} />}
      </YStack>
      {generatingSummary && (
        <View
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 9999,
          }}
        >
          <ActivityIndicator size="large" color="#1570EF" />
        </View>
      )}
      {contentDialogOpen && (
        <ContentDialog
          open={contentDialogOpen}
          onClose={setContentDialogOpen}
          title={contentDialog.title}
          body={contentDialog.body}
          btnText={contentDialog.btnText}
        />
      )}
    </View>
  );
}
