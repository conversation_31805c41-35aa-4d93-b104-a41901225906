export const useCallDetailStyles = () => {
  return {
    container: { flex: 1, backgroundColor: "$screenBackgroundcolor" },
    mainStack: { flex: 1, marginBlock: 20, marginInline: 20 },
    componentStack: {
      flex: 1,
      marginBlock: 20,
      backgroundColor: "$screenBackgroundcolor",
    },
    ConsultationData: { marginBlockStart: 1 },
    dottedLineContainer: {
      width: "100%" as "100%",
      alignItems: "flex-start",
      marginBlockStart: 20,
    },
    dottedLine: {
      flexDirection: "row" as "row",
      width: "100%" as "100%",
      height: 2,
      backgroundColor: "transparent" as "transparent",
    },
    dot: {
      width: 5,
      height: 2,
      backgroundColor: "$primaryBorderColor",
      marginRight: 5,
    },
    completeVisitBtn: {
      backgroundColor: "$primaryColor",
      color: "$buttonWhiteColor" as any,
      fontSize: 20,
      size: "$5" as "$5",
      fontWeight: "500" as "500",
      borderWidth: 2,
      // marginBlockStart: 10,
    },
    saveBtn: {
      backgroundColor: "$screenBackgroundcolor",
      color: "$textcolor" as any,
      fontSize: 20,
      size: "$5" as "$5",
      fontWeight: "500" as "500",
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
    },
    spinner: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "$screenBackgroundcolor",
    },
    vitalBoxContainer: {
      width: 90 as any,
      minH: 100 as any,
      borderWidth: 1 as any,
      borderColor: "$borderColor" as any,
      borderBottomLeftRadius: 8 as any,
      borderBottomRightRadius: 8 as any,
      borderTopLeftRadius: 8 as any,
      borderTopRightRadius: 8 as any,
      alignItems: "center" as any,
      justifyContent: "space-between" as any,
      padding: "$2" as any,
    },
    dateAndTimeText: {
      fontSize: 10,
      textAlign: "center" as any,
      mt: 6 as any,
      color: "$orderConfirmedTextColor" as any,
    },
    footerBar: {
      position: "absolute" as any,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "$screenBackgroundcolor" as any,
      borderTopWidth: 1 as any,
      borderTopColor: "$borderColor" as any,
      padding: 8 as any,
      paddingBottom: 8 as any,
    },
  };
};
