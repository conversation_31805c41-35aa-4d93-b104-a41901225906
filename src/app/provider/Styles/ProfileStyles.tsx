export const useProfileStyles = () => {
  return {
    mainContainer: {
      flex: 1,
    },
    childContainer: {
      flex: 1,
    },
    profileText: {
      fontSize: 20,
      fontWeight: "600" as any,
      marginBottom: 10,
    },
    dottedLineContainer: {
      width: "100%" as "100%",
      alignItems: "flex-start",
      marginBlockStart: 10,
    },
    dottedLine: {
      flexDirection: "row" as "row",
      width: "100%" as "100%",
      height: 2,
      backgroundColor: "transparent" as "transparent",
    },
    dot: {
      width: 5,
      height: 2,
      backgroundColor: "$primaryBorderColor",
      marginRight: 5,
    },
    vitalBoxContainer: {
      width: 90,
      minH: 100,
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      borderBottomLeftRadius: 8,
      borderBottomRightRadius: 8,
      borderTopLeftRadius: 8,
      borderTopRightRadius: 8,
      alignItems: "center" as any,
      justifyContent: "space-between" as any,
      padding: "$2" as any,
    },
    vitalBoxDateAndTimeContainer: {
      fontSize: 10,
      textAlign: "center" as any,
      mt: 6,
      color: "$placeholdertextColor" as any,
    },
    imageContainer: {
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
      borderRadius: 10,
      backgroundColor: "$screenBackgroundcolor",
      marginBlock: 20,
    },
    imageContainerBody: {
      marginBlock: 10,
      marginInline: 10,
    },
    attachmentsText: {
      fontSize: 16,
      fontWeight: "600" as any,
      marginBlockEnd: 10,
    },
  };
};
