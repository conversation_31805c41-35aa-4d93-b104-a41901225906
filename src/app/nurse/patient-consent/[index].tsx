import { useLocalSearchPara<PERSON>, useRouter } from "expo-router";
import { useState, useEffect, useRef, useMemo } from "react";
import { PatientData, PatientInfo, SelectedImage } from "../requestVisit";
import { But<PERSON>, ScrollView, Text, TextArea, View, YStack } from "tamagui";
import ScreenHeader from "src/components/ScreenHeader";
import SheetDemo from "src/components/SettingsDrawer";
import { useStyles } from "./styles/style";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import AddDocuments from "src/components/AddDocuments";
import ScanDocumentComponent from "src/components/ScanDocument";
import { SelectDocument } from "src/components/SelectDocuments";
import TelehealthConsent from "src/components/TelehealthConsent";
import axiosConfig from "~/services/axiosConfig";
import { uploadToS3 } from "src/utils/s3Upload";
import { ActivityIndicator, Keyboard } from "react-native";

export default function PatientConsentScreen() {
  const { index, selectedPatients, patientData } = useLocalSearchParams();
  const router = useRouter();
  const currentIndex = parseInt(index as string);
  const patients: PatientInfo[] = JSON.parse(selectedPatients as string);
  const parsedPatientData = useMemo(() => {
    return patientData ? JSON.parse(patientData as string) : [];
  }, [patientData]);

  const patient = patients[currentIndex];
  const [chiefComplaint, setChiefComplaint] = useState<string>("");
  const [scannedImages, setScannedImages] = useState<SelectedImage[]>([]);
  const [localPatientData, setLocalPatientData] = useState<PatientData[]>([]);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    if (parsedPatientData?.length > 0) {
      setLocalPatientData(parsedPatientData);
      const currentPatientData = parsedPatientData.find(
        (data: PatientData) => data.patient.id === patient.id
      );
      if (currentPatientData) {
        setChiefComplaint(currentPatientData.chiefComplaint || "");
        setScannedImages(currentPatientData.selectedImages || []);
      }
    }
  }, [parsedPatientData, patient.id]);

  const handleChiefComplaintChange = (text: string) => {
    setChiefComplaint(text);

    setLocalPatientData((prev: PatientData[]) => {
      if (prev.length === 0) {
        return patients.map((p) => ({
          patient: p,
          chiefComplaint: p.id === patient.id ? text : "",
          selectedImages: [],
        }));
      }

      return prev.map((patientData) =>
        patientData.patient.id === patient.id
          ? { ...patientData, chiefComplaint: text }
          : patientData
      );
    });
  };

  useEffect(() => {
    if (scannedImages) {
      setLocalPatientData((prev: PatientData[]) => {
        if (prev.length === 0) {
          return patients.map((p) => ({
            patient: p,
            chiefComplaint: "",
            selectedImages: p.id === patient.id ? scannedImages : [],
          }));
        }

        return prev.map((patientData) =>
          patientData.patient.id === patient.id
            ? { ...patientData, selectedImages: scannedImages }
            : patientData
        );
      });
    }
  }, [scannedImages]);

  const [openSettingsDrawer, setOpenSettingsDrawer] = useState(false);
  const [showScanDocument, setShowScanDocument] = useState(false);
  const [showUploader, setShowUploader] = useState(false);
  const styles = useStyles();

  const scrollViewRef = useRef<any>(null);

  const handleBack = () => {
    if (currentIndex > 0) {
      router.push({
        pathname: "/nurse/patient-consent/[index]",
        params: {
          index: (currentIndex - 1).toString(),
          patientData: JSON.stringify(localPatientData),
          selectedPatients,
        },
      });
    } else {
      router.back();
    }
  };

  const uploadImages = async () => {
    try {
      setUploading(true);

      let updatedImages = [...scannedImages];

      for (let i = 0; i < updatedImages.length; i++) {
        const img = updatedImages[i];
        if (img.info.fileKeys && img.info.fileKeys.length > 0) {
          continue;
        }

        const uri = img.url;
        const fileType = "image/jpeg";
        const fileName = `photo-${Date.now()}-${i}.jpg`;

        const response = await axiosConfig.get(
          `/user/upload-url?fileName=${encodeURIComponent(fileName)}&contentType=${encodeURIComponent(fileType)}&folder=consultations/documents`
        );

        if (response.status !== 200) {
          throw new Error("Failed to get S3 upload URL");
        }

        const { uploadUrl, key } = response.data;
        await uploadToS3(uploadUrl, uri, fileType);
        updatedImages[i] = {
          ...img,
          info: {
            fileKeys: [key],
            fileNames: [fileName],
            fileTypes: [fileType],
          },
        };
      }
      setScannedImages(updatedImages);
      setLocalPatientData((prev) =>
        prev.map((pd) =>
          pd.patient.id === patient.id
            ? { ...pd, selectedImages: updatedImages }
            : pd
        )
      );
      const nextPatientData = localPatientData.map((pd) =>
        pd.patient.id === patient.id
          ? { ...pd, selectedImages: updatedImages }
          : pd
      );

      if (currentIndex < patients.length - 1) {
        router.push({
          pathname: "/nurse/patient-consent/[index]",
          params: {
            index: (currentIndex + 1).toString(),
            patientData: JSON.stringify(nextPatientData),
            selectedPatients,
          },
        });
      } else {
        router.push({
          pathname: "/nurse/telehealthconsent",
          params: {
            patientData: JSON.stringify(nextPatientData),
            selectedPatients,
            isSinglePatient: 0,
          },
        });
      }
    } catch (err) {
      console.log("Error uploading images:", err);
    } finally {
      setUploading(false);
    }
  };

  const handleNext = () => {
    uploadImages();
  };

  const handleAddDocument = () => {
    Keyboard?.dismiss();
    if (scannedImages.length >= 3) {
      return;
    }
    setShowUploader(true);
  };

  const handleRemoveImage = (url: string) => {
    setScannedImages((prev) => prev.filter((u) => u.url !== url));
  };

  const handleCloseScanDocument = (images: SelectedImage[]) => {
    setShowScanDocument(false);
    setScannedImages(images);
  };

  const handleOpenScanDocument = () => {
    setShowUploader(false);
    setShowScanDocument(true);
  };

  const handleAddGalleryImage = (uri: string) => {
    setScannedImages((prev) => [
      ...prev,
      {
        url: uri,
        info: {
          fileKeys: [],
          fileNames: [],
          fileTypes: [],
        },
      },
    ]);
  };

  return (
    <View {...styles.container}>
      <KeyboardAwareScrollView
        innerRef={(ref) => (scrollViewRef.current = ref)}
        contentContainerStyle={{ flexGrow: 1 }}
        enableOnAndroid
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <View {...styles.mainStack}>
          <ScreenHeader
            onAvatarPress={() => setOpenSettingsDrawer(true)}
            screenName="Back"
            onBackPress={handleBack}
          />

          <Text {...styles.patinetCountText}>
            PATIENT {currentIndex + 1} OF {patients?.length}
          </Text>
          <Text {...styles.telehealthConsentText}>
            Telehealth Informed Consent
          </Text>

          <View {...styles.patientCard}>
            <YStack gap={"$2"}>
              <Text {...styles.patientCardTitle}>{patient?.name}</Text>
              <Text {...styles.patientCardSubTitle}>DOB: {patient?.dob}</Text>
            </YStack>
          </View>

          <Text {...styles.complaintText}>Chief complaint</Text>
          <TextArea
            {...styles.complaintTextArea}
            placeholder="Please enter the details."
            placeholderTextColor={"$textcolor"}
            overflow="hidden"
            value={chiefComplaint}
            onChangeText={handleChiefComplaintChange}
          />
          <YStack>
            <Text {...styles.attachmentsText}>Attachments</Text>
            <AddDocuments
              onAddDocument={handleAddDocument}
              scannedImages={scannedImages}
              onRemoveImage={handleRemoveImage}
              patientName={patient?.name}
            />
          </YStack>

          <YStack {...styles.telehealthConsentTextContainer}>
            <ScrollView showsVerticalScrollIndicator={false}>
              <TelehealthConsent />
            </ScrollView>
          </YStack>
        </View>
      </KeyboardAwareScrollView>

      <View {...styles.nextBtnContainer}>
        <Button
          {...(chiefComplaint.trim() === ""
            ? styles.nextBtnDisabled
            : styles.nextBtn)}
          disabled={chiefComplaint.trim() === ""}
          onPress={handleNext}
        >
          Agree & Next
        </Button>
      </View>

      {showScanDocument && (
        <View
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 9999,
            backgroundColor: "#fff",
          }}
        >
          <ScanDocumentComponent
            open={showScanDocument}
            initialImages={scannedImages}
            onClose={handleCloseScanDocument}
            maxImages={3}
          />
        </View>
      )}
      {!showScanDocument && showUploader && (
        <SelectDocument
          open={showUploader}
          onClose={setShowUploader}
          onOpenScanDocument={handleOpenScanDocument}
          onAddImage={handleAddGalleryImage}
          scannedImages={scannedImages}
        />
      )}
      {uploading && (
        <View
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0,0,0,0.3)",
            zIndex: 9999,
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <ActivityIndicator size="large" color="#1570EF" />
          <Text style={{ color: "#1570EF", marginTop: 16, fontSize: 18 }}>
            Uploading images...
          </Text>
        </View>
      )}

      {openSettingsDrawer && (
        <SheetDemo open={openSettingsDrawer} setOpen={setOpenSettingsDrawer} />
      )}
    </View>
  );
}
