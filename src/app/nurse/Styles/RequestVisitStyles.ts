export const useRequestVisitStyle = () => {
  return {
    container: {
      flex: 1,
      backgroundColor: "$screenBackgroundcolor",
    },
    mainStack: {
      marginBlock: 20,
      marginInline: 20,
      backgroundColor: "$screenBackgroundcolor",
      flex: 1,
    },
    profileInfoCard: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
      borderRadius: 12,
      padding: 20,
      marginBlockStart: 30,
      marginBlockEnd: 20,
    },
    profileInfoCardContainer: {
      justifyContent: "space-between" as any,
    },
    signOutText: {
      color: "$primaryColor" as any,
      fontSize: 14,
      fontWeight: 400 as any,
    },
    avatarContainer: {
      circular: true,
      size: "$5" as any,
      borderColor: "$primaryBorderColor" as any,
    },
    avatarFallBack: {
      backgroundColor: "#1570EF" as any,
      display: "flex" as any,
      justifyContent: "center" as any,
      alignItems: "center" as any,
    },
    nameText: {
      fontSize: 14,
      fontWeight: 500 as any,
    },
    emailText: { fontSize: 14, fontWeight: 400 as any },
    requestVisitContainer: {
      marginBlockStart: 10,
      flex: 1,
    },
    patientConsentContainer: {
      marginInline: 20,
      marginBlockEnd: 20,
    },
    patientConsentSubContainer: {
      maxW: 400,
      gap: "$3" as any,
      self: "center" as any,
      width: "100%" as any,
    },
    patientConsentBtnDisabled: {
      backgroundColor: "$disableButtonPrimaryColor" as any,
      borderColor: "$disableButtonPrimaryColor" as any,
      borderRadius: 8,
      color: "$buttonWhiteColor" as any,
      fontWeight: "600" as any,
      size: "$4" as any,
      fontSize: 16,
    },
    patientConsentBtn: {
      backgroundColor: "$primaryColor" as any,
      borderColor: "$primaryColor" as any,
      borderRadius: 8,
      color: "$buttonWhiteColor" as any,
      fontWeight: "600" as any,
      size: "$4" as any,
      fontSize: 16,
    },
    cancelBtn: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: 8,
      color: "$textcolor" as any,
      fontWeight: "600" as any,
      size: "$4" as any,
      fontSize: 16,
    },
    addPatientBtn: {
      borderWidth: 1,
      padding: 7,
      fontSize: 14,
      size: "$4" as any,
      fontWeight: "600" as any,
      backgroundColor: "$confirmOrderBlue",
      borderColor: "$confirmOrderBorderCOlor" as any,
      color: "$confirmOrderTextColor" as any,
      marginBlockStart: 30,
    },
    patientsText: {
      fontWeight: 600 as any,
      fontSize: 14,
      marginBlock: 20,
    },
  };
};
