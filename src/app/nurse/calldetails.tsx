import { Plus } from "@tamagui/lucide-icons";
import { useState } from "react";
import { FlatList } from "react-native";
import { Addpatients } from "src/components/AddPatientsDialog";
import { PatientQueue } from "src/components/PatientQueue";

import ScreenHeader from "src/components/ScreenHeader";
import { Button, Text, XStack, YStack } from "tamagui";

export type demoPatients = {
  name: string;
  gender: string;
  dob: string;
  id: string;
};

export default function CallDetails({
  goToCall,
  endCall,
  isFromCall = false,
}: {
  goToCall: () => void;
  endCall: () => void;
  isFromCall?: boolean;
}) {
  const [patients, setPatients] = useState<demoPatients[]>([
    {
      id: "1",
      name: "<PERSON>",
      gender: "Male",
      dob: "1985-06-15",
    },
    {
      id: "2",
      name: "Da<PERSON>ys Targaryen",
      gender: "Female",
      dob: "1990-11-20",
    },
    {
      id: "3",
      name: "<PERSON><PERSON>",
      gender: "Female",
      dob: "1978-02-03",
    },
    {
      id: "4",
      name: "<PERSON>",
      gender: "Male",
      dob: "2001-10-27",
    },
  ]);
  const [dialogOpen, setDialogOpen] = useState(false);

  const renderItem = (item: demoPatients) => (
    <YStack>
      <YStack {...styles.listItemContainer}>
        <YStack>
          <Text {...styles.nameText}>{item.name}</Text>
        </YStack>
        <XStack>
          <Text {...styles.subText}>Gender: {item.gender}</Text>
          <Text {...styles.separator}>|</Text>
          <Text {...styles.subText}>DOB: {item.dob}</Text>
        </XStack>
      </YStack>
    </YStack>
  );

  return (
    <YStack {...styles.container}>
      <YStack {...styles.subContainer}>
        <ScreenHeader
          screenName="Back to Call"
          onBackPress={goToCall}
          shouldSHowChatIcon={false}
          shouldShowAvatar={false}
        />
        <PatientQueue
          patients={patients}
          setDialogOpen={setDialogOpen}
          renderItem={renderItem}
          endCall={endCall}
        />
      </YStack>
      {dialogOpen && (
        <Addpatients open={dialogOpen} onClose={() => setDialogOpen(false)} />
      )}
    </YStack>
  );
}

const styles = {
  subContainer: {
    marginBlockStart: 20,
    marginInline: 20,
    flex: 1,
  },
  container: { flex: 1, backgroundColor: "$screenBackgroundcolor" },
  addToQueueBtn: {
    marginBlockStart: 20,
    fontSize: 16,
    size: "$4" as any,
    fontWeight: "600" as any,
    backgroundColor: "$confirmOrderBlue",
    borderColor: "$confirmOrderBorderCOlor" as any,
    color: "$confirmOrderTextColor" as any,
    borderWidth: 1,
  },
  patientQueueText: {
    fontSize: 14,
    fontWeight: "600" as any,
    color: "$textcolor" as any,
    marginBlockStart: 30,
    marginBlockEnd: 20,
  },
  listItemContainer: {
    borderWidth: 1,
    borderColor: "$primaryBorderColor" as any,
    padding: 15,
    marginBlockEnd: 20,
    borderRadius: 10,
    gap: "$2" as any,
  },
  separator: {
    fontSize: 16,
    fontWeight: "200" as "200",
    marginInline: 5,
  },
  nameText: {
    color: "$textcolor" as any,
    fontWeight: 600 as any,
    fontSize: 16,
  },
  subText: {
    color: "$textcolor" as any,
    fontWeight: 400 as any,
    fontSize: 14,
  },
  endCallBtn: {
    backgroundColor: "$primaryColor" as any,
    color: "$buttonWhiteColor" as any,
    fontWeight: "600" as any,
    marginBlockStart: 10,
    size: "$4" as any,
    fontSize: 16,
    marginBlockEnd: 10,
  },
};
